{"skeleton": {"hash": "eeU/VJTMrWXNSvPRpkgVc8KvDwE", "spine": "3.8.99", "x": -106.92, "y": -1.57, "width": 220.58, "height": 204.95, "images": "./images/", "audio": "E:/lu/练习/wb/2025.2.11音乐游戏/Scary Music Beatbox/spine/1-10"}, "bones": [{"name": "root", "scaleX": 0.8684, "scaleY": 0.8684}, {"name": "bone", "parent": "root"}, {"name": "bone2", "parent": "bone", "x": 0.28, "y": 100.8}, {"name": "bone3", "parent": "bone2", "x": 0.16, "y": 80.78}, {"name": "bone4", "parent": "bone3", "x": -19.83, "y": 4.52}, {"name": "bone5", "parent": "bone3", "x": 36.9, "y": 6.28}, {"name": "bone6", "parent": "bone3", "x": 8.28, "y": -16.82}], "slots": [{"name": "1-10<PERSON><PERSON>", "bone": "bone", "attachment": "1-10<PERSON><PERSON>"}, {"name": "1-10zuiba1", "bone": "bone6", "attachment": "1-10zuiba1"}, {"name": "1-10<PERSON>jin2", "bone": "bone4", "attachment": "1-10<PERSON>jin2"}, {"name": "1-10<PERSON>jin1", "bone": "bone5", "attachment": "1-10<PERSON>jin1"}], "skins": [{"name": "default", "attachments": {"1-10shenti": {"1-10shenti": {"type": "mesh", "hull": 4, "width": 254, "height": 236, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 3, 0, 1, 2, 3], "vertices": [2, 3, 130.45, -183.39, 0.00635, 2, 130.6, -102.61, 0.99365, 2, 3, -123.55, -183.39, 0.00583, 2, -123.4, -102.61, 0.99417, 2, 3, -123.55, 52.61, 0.88393, 2, -123.4, 133.39, 0.11607, 2, 3, 130.45, 52.61, 0.87079, 2, 130.6, 133.39, 0.12921], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "1-10yanjin1": {"1-10yanjin1": {"type": "mesh", "hull": 4, "width": 26, "height": 31, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [13.55, -16.66, -12.45, -16.66, -12.45, 14.34, 13.55, 14.34], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}, "1-10yanjing3": {"type": "mesh", "hull": 4, "width": 25, "height": 11, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [12.55, -9.19, -12.45, -9.19, -12.45, 1.81, 12.55, 1.81], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "1-10yanjin2": {"1-10yanjin2": {"type": "mesh", "hull": 4, "width": 26, "height": 31, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [12.28, -14.91, -13.72, -14.91, -13.72, 16.09, 12.28, 16.09], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}, "1-10yanjing4": {"type": "mesh", "hull": 4, "width": 31, "height": 7, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [15.28, -5.57, -15.72, -5.57, -15.72, 1.43, 15.28, 1.43], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "1-10zuiba1": {"1-10zuiba1": {"type": "mesh", "hull": 4, "width": 26, "height": 17, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [13.16, -8.57, -12.84, -8.57, -12.84, 8.43, 13.16, 8.43], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}, "1-10zuiba2": {"type": "mesh", "hull": 4, "width": 25, "height": 8, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [13.16, -3.57, -11.84, -3.57, -11.84, 4.43, 13.16, 4.43], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}}}], "animations": {"animation": {"slots": {"1-10yanjin1": {"attachment": [{"time": 0.3333, "name": "1-10<PERSON>jing3"}, {"time": 0.3667, "name": "1-10<PERSON>jin1"}, {"time": 0.8333, "name": "1-10<PERSON>jing3"}, {"time": 0.8667, "name": "1-10<PERSON>jin1"}]}, "1-10yanjin2": {"attachment": [{"time": 0.1667, "name": "1-10<PERSON><PERSON><PERSON>4"}, {"time": 0.2, "name": "1-10<PERSON>jin2"}, {"time": 0.6667, "name": "1-10<PERSON><PERSON><PERSON>4"}, {"time": 0.7, "name": "1-10<PERSON>jin2"}]}, "1-10zuiba1": {"attachment": [{"time": 0.2333, "name": "1-10zuiba2"}, {"time": 0.3667, "name": "1-10zuiba1"}, {"time": 0.7333, "name": "1-10zuiba2"}, {"time": 0.8667, "name": "1-10zuiba1"}]}}, "bones": {"bone6": {"translate": [{"x": -0.79, "y": -3.34, "curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.5, "x": -0.79, "y": -3.34, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8667, "curve": "stepped"}, {"time": 1, "x": -0.79, "y": -3.34}], "scale": [{"x": 0.701, "y": 1.723, "curve": "stepped"}, {"time": 0.1333, "curve": "stepped"}, {"time": 0.3667, "curve": "stepped"}, {"time": 0.5, "x": 0.701, "y": 1.723, "curve": "stepped"}, {"time": 0.6333, "curve": "stepped"}, {"time": 0.8667, "curve": "stepped"}, {"time": 1, "x": 0.701, "y": 1.723}]}, "bone4": {"translate": [{}, {"time": 0.1333, "y": -2.22, "curve": "stepped"}, {"time": 0.1667}, {"time": 0.2, "y": -2.22}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5}, {"time": 0.6333, "y": -2.22, "curve": "stepped"}, {"time": 0.6667}, {"time": 0.7, "y": -2.22}, {"time": 0.8333}], "scale": [{}, {"time": 0.1333, "y": 0.211, "curve": "stepped"}, {"time": 0.1667}, {"time": 0.2, "y": 0.211}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.5}, {"time": 0.6333, "y": 0.211, "curve": "stepped"}, {"time": 0.6667}, {"time": 0.7, "y": 0.211}, {"time": 0.8333}]}, "bone5": {"translate": [{"time": 0.1667}, {"time": 0.3, "y": -3.5, "curve": "stepped"}, {"time": 0.3333, "y": 0.6, "curve": "stepped"}, {"time": 0.3667, "y": -3.5}, {"time": 0.5, "curve": "stepped"}, {"time": 0.6667}, {"time": 0.8, "y": -3.5, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 0.8667, "y": -3.5}, {"time": 1}], "scale": [{"time": 0.1667}, {"time": 0.3, "y": 0.12, "curve": "stepped"}, {"time": 0.3333, "curve": "stepped"}, {"time": 0.3667, "y": 0.12}, {"time": 0.5, "curve": "stepped"}, {"time": 0.6667}, {"time": 0.8, "y": 0.12, "curve": "stepped"}, {"time": 0.8333, "curve": "stepped"}, {"time": 0.8667, "y": 0.12}, {"time": 1}]}}}}}