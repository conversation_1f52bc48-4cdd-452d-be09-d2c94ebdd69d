{"skeleton": {"hash": "QlA0dsNlrWibPQ3O0ZNnj2tW3q8", "spine": "3.8.99", "x": -104.54, "y": -2.35, "width": 195.54, "height": 268.65, "images": "./images/", "audio": "E:/lu/练习/wb/2025.2.11音乐游戏/Scary Music Beatbox/spine/系列1/1-14"}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "scaleX": 0.9138, "scaleY": 0.9138}, {"name": "bone2", "parent": "bone", "length": 164.74, "rotation": 90, "x": 0.41, "y": 0.93}, {"name": "bone3", "parent": "bone2", "length": 101.4, "x": 163.68}, {"name": "bone4", "parent": "bone2", "x": 65.51, "y": -0.02}, {"name": "bone5", "parent": "bone2", "x": 86.46, "y": 0.26}], "slots": [{"name": "1-14mogu3", "bone": "bone", "attachment": "1-14mogu3"}, {"name": "1-14<PERSON><PERSON>", "bone": "bone", "attachment": "1-14<PERSON><PERSON>"}, {"name": "1-14mogu2", "bone": "bone", "attachment": "1-14mogu2"}, {"name": "1-14zu<PERSON>", "bone": "bone", "attachment": "1-14zu<PERSON>"}, {"name": "1-14<PERSON><PERSON>", "bone": "bone", "attachment": "1-14<PERSON><PERSON>"}, {"name": "1-14mogu1", "bone": "bone", "attachment": "1-14mogu1"}], "skins": [{"name": "default", "attachments": {"1-14mogu1": {"1-14mogu1": {"type": "mesh", "hull": 4, "width": 180, "height": 131, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 3, -4.18, -89.18, 1, 1, 3, -4.18, 90.82, 1, 1, 3, 126.82, 90.82, 1, 1, 3, 126.82, -89.18, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "1-14mogu2": {"1-14mogu2": {"type": "mesh", "hull": 4, "width": 105, "height": 79, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 3, -51.18, 9.82, 1, 1, 3, -51.18, 114.82, 1, 1, 3, 27.82, 114.82, 1, 1, 3, 27.82, 9.82, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "1-14mogu3": {"1-14mogu3": {"type": "mesh", "hull": 4, "width": 96, "height": 61, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 3, -48.18, -99.18, 1, 1, 3, -48.18, -3.18, 1, 1, 3, 12.82, -3.18, 1, 1, 3, 12.82, -99.18, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "1-14shenti": {"1-14shenti": {"type": "mesh", "hull": 4, "width": 131, "height": 189, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 2, -3.5, -65.18, 1, 1, 2, -3.5, 65.82, 1, 1, 2, 185.5, 65.82, 1, 1, 2, 185.5, -65.18, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "1-14yanjin": {"1-14yanjin": {"type": "mesh", "hull": 4, "width": 56, "height": 37, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 5, -12.96, -29.44, 1, 1, 5, -12.96, 26.56, 1, 1, 5, 24.04, 26.56, 1, 1, 5, 24.04, -29.44, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "1-14zuiba": {"1-14zuiba": {"type": "mesh", "hull": 4, "width": 32, "height": 29, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 4, -17.01, -16.16, 1, 1, 4, -17.01, 15.84, 1, 1, 4, 11.99, 15.84, 1, 1, 4, 11.99, -16.16, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}}}], "animations": {"animation": {"bones": {"bone3": {"rotate": [{"angle": 0.15, "curve": 0.338, "c2": 0.35, "c3": 0.674, "c4": 0.69}, {"time": 0.0667, "angle": 1.74, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.2333, "angle": 4.38, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "angle": -4.91, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 1, "angle": 0.15, "curve": 0.338, "c2": 0.35, "c3": 0.674, "c4": 0.69}, {"time": 1.0667, "angle": 1.74, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 1.2333, "angle": 4.38, "curve": 0.25, "c3": 0.75}, {"time": 1.7333, "angle": -4.91, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 2, "angle": 0.15}], "translate": [{"x": -2.06, "curve": 0.298, "c2": 0.2, "c3": 0.641, "c4": 0.57}, {"time": 0.0667, "x": -1.12, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 0.2333, "x": 1.13, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "x": -2.26, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "x": 1.13, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "x": -2.26, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 1, "x": -2.06, "curve": 0.298, "c2": 0.2, "c3": 0.641, "c4": 0.57}, {"time": 1.0667, "x": -1.12, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 1.2333, "x": 1.13, "curve": 0.25, "c3": 0.75}, {"time": 1.4667, "x": -2.26, "curve": 0.25, "c3": 0.75}, {"time": 1.7333, "x": 1.13, "curve": 0.25, "c3": 0.75}, {"time": 1.9667, "x": -2.26, "curve": 0.299, "c3": 0.636, "c4": 0.36}, {"time": 2, "x": -2.06}]}, "bone4": {"translate": [{"x": -0.37, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "x": -5.26, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 1, "x": -0.37, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 1.0667, "curve": 0.25, "c3": 0.75}, {"time": 1.5667, "x": -5.26, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 2, "x": -0.37}], "scale": [{"x": 1.034, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "x": 1.483, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 1, "x": 1.034, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 1.0667, "curve": 0.25, "c3": 0.75}, {"time": 1.5667, "x": 1.483, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 2, "x": 1.034}]}, "bone5": {"translate": [{"x": -0.08, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "x": -1.11, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 1, "x": -0.08, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 1.0667, "curve": 0.25, "c3": 0.75}, {"time": 1.5667, "x": -1.11, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 2, "x": -0.08}], "scale": [{"x": 1.012, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "x": 1.169, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 1, "x": 1.012, "curve": 0.366, "c2": 0.63, "c3": 0.703}, {"time": 1.0667, "curve": 0.25, "c3": 0.75}, {"time": 1.5667, "x": 1.169, "curve": 0.244, "c3": 0.7, "c4": 0.79}, {"time": 2, "x": 1.012}]}}}}}