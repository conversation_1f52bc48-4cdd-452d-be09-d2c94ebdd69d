{"skeleton": {"hash": "uMIeQvwSbbLESKMEwv0cwIxLZm0", "spine": "3.8.99", "x": -78.66, "y": -2.68, "width": 153.66, "height": 204.55, "images": "./images/", "audio": "E:/lu/练习/wb/2025.2.11音乐游戏/Scary Music Beatbox/spine/系列1/1-19"}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "scaleX": 0.9787, "scaleY": 0.9787}, {"name": "bone2", "parent": "bone", "length": 98.16, "rotation": 90, "x": 0.45, "y": 0.22}, {"name": "bone2b", "parent": "bone2", "length": 98.16, "x": 98.16}, {"name": "bone2b2", "parent": "bone2b", "x": 53.26, "y": 25.09}, {"name": "bone2b3", "parent": "bone2b", "x": 53.26, "y": -21.32}, {"name": "bone2b4", "parent": "bone2b", "x": 75.92, "y": 22.91}, {"name": "bone2b5", "parent": "bone2b", "x": 76.74, "y": -19.95}], "slots": [{"name": "1-19<PERSON><PERSON>", "bone": "bone", "attachment": "1-19<PERSON><PERSON>"}, {"name": "1-19<PERSON><PERSON>", "bone": "bone", "attachment": "1-19<PERSON><PERSON>"}, {"name": "1-19<PERSON><PERSON>2", "bone": "bone", "attachment": "1-19<PERSON><PERSON>2"}, {"name": "1-19<PERSON>jin1", "bone": "bone", "attachment": "1-19<PERSON>jin1"}, {"name": "1-19meimao2", "bone": "bone", "attachment": "1-19meimao2"}, {"name": "1-19meimao1", "bone": "bone", "attachment": "1-19meimao1"}], "skins": [{"name": "default", "attachments": {"1-19meimao1": {"1-19meimao1": {"type": "mesh", "hull": 4, "width": 25, "height": 10, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 6, -4.03, -12.09, 1, 1, 6, -4.03, 12.91, 1, 1, 6, 5.97, 12.91, 1, 1, 6, 5.97, -12.09, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "1-19meimao2": {"1-19meimao2": {"type": "mesh", "hull": 4, "width": 20, "height": 12, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 7, -5.85, -9.23, 1, 1, 7, -5.85, 10.77, 1, 1, 7, 6.15, 10.77, 1, 1, 7, 6.15, -9.23, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "1-19shenti": {"1-19shenti": {"type": "mesh", "hull": 4, "width": 109, "height": 101, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 2, -2.96, -52.18, 1, 1, 2, -2.96, 56.82, 1, 1, 2, 98.04, 56.82, 1, 1, 2, 98.04, -52.18, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "1-19tou": {"1-19tou": {"type": "mesh", "hull": 4, "width": 157, "height": 155, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 3, -47.12, -76.18, 1, 1, 3, -47.12, 80.82, 1, 1, 3, 107.88, 80.82, 1, 1, 3, 107.88, -76.18, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "1-19yanjin1": {"1-19yanjin1": {"type": "mesh", "hull": 4, "width": 36, "height": 42, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 4, -20.38, -17.28, 1, 1, 4, -20.38, 18.72, 1, 1, 4, 21.62, 18.72, 1, 1, 4, 21.62, -17.28, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "1-19yanjin2": {"1-19yanjin2": {"type": "mesh", "hull": 4, "width": 35, "height": 41, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 5, -19.38, -16.87, 1, 1, 5, -19.38, 18.13, 1, 1, 5, 21.62, 18.13, 1, 1, 5, 21.62, -16.87, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}}}], "animations": {"animation": {"bones": {"bone2b": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 1.4, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -1.58, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "angle": 1.4, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -1.58, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 1.4, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "angle": -1.58, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "angle": 1.4, "curve": 0.25, "c3": 0.75}, {"time": 1}], "translate": [{"x": -10.46, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "curve": "stepped"}, {"time": 0.7, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": -10.46}]}, "bone2b2": {"translate": [{"x": -6.8, "y": -4.65, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "y": -3.59, "curve": "stepped"}, {"time": 0.7, "y": -3.59, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": -6.8, "y": -4.65}]}, "bone2b3": {"translate": [{"x": -6.8, "y": -4.65, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "y": -3.59, "curve": "stepped"}, {"time": 0.7, "y": -3.59, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": -6.8, "y": -4.65}]}, "bone2b4": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 28.68, "curve": "stepped"}, {"time": 0.7, "angle": 28.68, "curve": 0.25, "c3": 0.75}, {"time": 1}], "translate": [{"y": -2.34, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "y": -0.88, "curve": "stepped"}, {"time": 0.7, "y": -0.88, "curve": 0.25, "c3": 0.75}, {"time": 1, "y": -2.34}]}, "bone2b5": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -37.5, "curve": "stepped"}, {"time": 0.7, "angle": -37.5, "curve": 0.25, "c3": 0.75}, {"time": 1}], "translate": [{"y": -2.34, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "y": -2.94, "curve": "stepped"}, {"time": 0.7, "y": -2.94, "curve": 0.25, "c3": 0.75}, {"time": 1, "y": -2.34}]}}}}}