{"skeleton": {"hash": "VZqyfiyrke97mHE5wD/qdzTlcrM", "spine": "3.8.99", "x": -77.48, "y": -3.08, "width": 155.43, "height": 205.54, "images": "./images/", "audio": "E:/lu/练习/wb/2025.2.11音乐游戏/Scary Music Beatbox/spine/1-2"}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "scaleX": 1.0226, "scaleY": 1.0226}, {"name": "bone2", "parent": "bone", "length": 72.91, "rotation": 89.81, "x": -0.09, "y": 0.32}, {"name": "bone3", "parent": "bone2", "length": 53.38, "rotation": 0.99, "x": 72.91}, {"name": "bone4", "parent": "bone3", "x": 32.9, "y": 0.12}, {"name": "bone5", "parent": "bone3", "x": 9.06, "y": -0.14}, {"name": "bone6", "parent": "bone3", "x": 54.78, "y": 0.4}], "slots": [{"name": "1-2<PERSON><PERSON>", "bone": "bone", "attachment": "1-2<PERSON><PERSON>"}, {"name": "1-2<PERSON><PERSON>", "bone": "bone", "attachment": "1-2<PERSON><PERSON>"}, {"name": "1-2zuiba", "bone": "bone", "attachment": "1-2zuiba"}, {"name": "1-2<PERSON><PERSON><PERSON>", "bone": "bone", "attachment": "1-2<PERSON><PERSON><PERSON>"}, {"name": "1-2biaoqing", "bone": "bone", "attachment": "1-2biaoqing"}], "skins": [{"name": "default", "attachments": {"1-2biaoqing": {"1-2biaoqing": {"type": "mesh", "hull": 4, "width": 50, "height": 21, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 4, -8.49, -25.53, 1, 1, 4, -7.8, 24.47, 1, 1, 4, 13.2, 24.18, 1, 1, 4, 12.5, -25.82, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "1-2meimao": {"1-2meimao": {"type": "mesh", "hull": 4, "width": 49, "height": 9, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 6, -6.36, -25.15, 1, 1, 6, -5.67, 23.85, 1, 1, 6, 3.33, 23.72, 1, 1, 6, 2.64, -25.27, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "1-2shenti": {"1-2shenti": {"type": "mesh", "hull": 4, "width": 104, "height": 109, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 2, -3.15, -52.32, 1, 1, 2, -3.5, 51.68, 1, 1, 2, 105.5, 52.05, 1, 1, 2, 105.85, -51.95, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "1-2tou": {"1-2tou": {"type": "mesh", "hull": 4, "width": 152, "height": 127, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 3, -3.3, -76.03, 1, 1, 3, -1.18, 75.96, 1, 1, 3, 125.81, 74.19, 1, 1, 3, 123.69, -77.8, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "1-2zuiba": {"1-2zuiba": {"type": "mesh", "hull": 4, "width": 27, "height": 14, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 5, -7.48, -12.95, 1, 1, 5, -7.11, 14.05, 1, 1, 5, 6.89, 13.85, 1, 1, 5, 6.52, -13.14, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}}}], "animations": {"animation": {"bones": {"bone3": {"translate": [{"x": 13.51, "y": 0.05, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": 9.56, "y": 0.03, "curve": "stepped"}, {"time": 0.2333, "x": 9.56, "y": 0.03, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": 13.51, "y": 0.05, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "x": 9.56, "y": 0.03, "curve": "stepped"}, {"time": 0.7333, "x": 9.56, "y": 0.03, "curve": 0.25, "c3": 0.75}, {"time": 1, "x": 13.51, "y": 0.05}], "scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "x": 0.976, "curve": "stepped"}, {"time": 0.2333, "x": 0.976, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "curve": 0.25, "c3": 0.75}, {"time": 0.6333, "x": 0.976, "curve": "stepped"}, {"time": 0.7333, "x": 0.976, "curve": 0.25, "c3": 0.75}, {"time": 1}]}, "bone4": {"translate": [{"x": 0.52, "y": -0.01, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.0333, "x": 0.73, "y": -0.01, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": -2.63, "y": 0.04, "curve": "stepped"}, {"time": 0.2667, "x": -2.63, "y": 0.04, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 0.5, "x": 0.52, "y": -0.01, "curve": 0.364, "c2": 0.64, "c3": 0.701}, {"time": 0.5333, "x": 0.73, "y": -0.01, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "x": -2.63, "y": 0.04, "curve": "stepped"}, {"time": 0.7667, "x": -2.63, "y": 0.04, "curve": 0.244, "c3": 0.702, "c4": 0.8}, {"time": 1, "x": 0.52, "y": -0.01}]}, "bone6": {"translate": [{"x": 7.4, "y": -0.1, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0667, "x": 8.44, "y": -0.12, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": 2.8, "y": -0.04, "curve": "stepped"}, {"time": 0.3, "x": 2.8, "y": -0.04, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.5, "x": 7.4, "y": -0.1, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.5667, "x": 8.44, "y": -0.12, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "x": 2.8, "y": -0.04, "curve": "stepped"}, {"time": 0.8, "x": 2.8, "y": -0.04, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 1, "x": 7.4, "y": -0.1}]}, "bone5": {"translate": [{"x": -0.9, "y": 0.01, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": -4.89, "y": 0.07, "curve": "stepped"}, {"time": 0.3, "x": -4.89, "y": 0.07, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.5, "x": -0.9, "y": 0.01, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.5667, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "x": -4.89, "y": 0.07, "curve": "stepped"}, {"time": 0.8, "x": -4.89, "y": 0.07, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 1, "x": -0.9, "y": 0.01}], "scale": [{"x": 1.158, "y": 0.934, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.0667, "y": 0.953, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": 1.857, "y": 0.848, "curve": "stepped"}, {"time": 0.3, "x": 1.857, "y": 0.848, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 0.5, "x": 1.158, "y": 0.934, "curve": 0.379, "c2": 0.6, "c3": 0.724}, {"time": 0.5667, "y": 0.953, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "x": 1.857, "y": 0.848, "curve": "stepped"}, {"time": 0.8, "x": 1.857, "y": 0.848, "curve": 0.242, "c3": 0.667, "c4": 0.67}, {"time": 1, "x": 1.158, "y": 0.934}]}}}}}