{"skeleton": {"hash": "3tJe2VbJqYOIjQqILUHGIDjcySg", "spine": "3.8.99", "x": -76.85, "y": -1.62, "width": 155.9, "height": 207.87, "images": "./images/", "audio": "E:/lu/练习/wb/2025.2.11音乐游戏/Scary Music Beatbox/spine/系列1/1-15"}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "scaleX": 0.464, "scaleY": 0.464}, {"name": "bone2", "parent": "bone", "length": 233.49, "rotation": 90.53, "x": -0.52, "y": 1.04}, {"name": "bone8", "parent": "bone2", "length": 96.17, "x": 231.43, "y": -0.6}, {"name": "bone3", "parent": "bone8", "length": 42.15, "rotation": 34.16, "x": 92.61, "y": 51.06}, {"name": "bone4", "parent": "bone3", "length": 59.29, "rotation": -15.45, "x": 42.15}, {"name": "bone5", "parent": "bone8", "length": 56.89, "rotation": -39.19, "x": 99.75, "y": -41.42}, {"name": "bone6", "parent": "bone5", "length": 59.77, "rotation": 27.52, "x": 57.59, "y": 0.56}, {"name": "bone7", "parent": "bone8", "x": 51.26, "y": -0.98}], "slots": [{"name": "1-15<PERSON>jin2", "bone": "bone", "attachment": "1-15<PERSON>jin2"}, {"name": "1-15<PERSON>jin1", "bone": "bone", "attachment": "1-15<PERSON>jin1"}, {"name": "1-15<PERSON><PERSON>", "bone": "bone", "attachment": "1-15<PERSON><PERSON>"}, {"name": "1-15<PERSON><PERSON>", "bone": "bone", "attachment": "1-15<PERSON><PERSON>"}, {"name": "1-15zu<PERSON>", "bone": "bone", "attachment": "1-15zu<PERSON>"}], "skins": [{"name": "default", "attachments": {"1-15gunzi": {"1-15gunzi": {"type": "mesh", "hull": 4, "width": 28, "height": 187, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 2, -4.66, -14.85, 1, 1, 2, -4.4, 13.15, 1, 1, 2, 182.6, 11.41, 1, 1, 2, 182.34, -16.59, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "1-15shenti": {"1-15shenti": {"type": "mesh", "hull": 4, "width": 336, "height": 223, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 3, -63.54, -171.86, 1, 1, 3, -60.42, 164.13, 1, 1, 3, 162.57, 162.05, 1, 1, 3, 159.45, -173.93, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}, "1-15yanjin1": {"1-15yanjin1": {"type": "mesh", "hull": 15, "width": 82, "height": 107, "uvs": [0.87878, 0.20594, 0.89708, 0.43345, 0.74864, 0.55811, 0.80721, 0.68909, 0.92312, 0.75921, 1, 0.79972, 1, 1, 0.77468, 1, 0.63641, 0.93374, 0.49611, 0.79661, 0.40006, 0.65706, 0.11539, 0.58227, 0, 0.41709, 0, 0, 0.67138, 0, 0.59323, 0.62122], "triangles": [12, 13, 14, 14, 15, 12, 14, 0, 15, 1, 2, 0, 12, 10, 11, 15, 0, 2, 15, 10, 12, 15, 2, 3, 9, 10, 15, 3, 9, 15, 8, 3, 4, 8, 9, 3, 7, 8, 4, 7, 4, 5, 7, 5, 6], "vertices": [1, 5, 32.11, -39.19, 1, 2, 4, 41.79, -33.7, 0.01862, 5, 8.63, -32.58, 0.98138, 2, 4, 37.75, -16.1, 0.39679, 5, 0.05, -16.69, 0.60321, 2, 4, 23.5, -12.07, 0.98577, 5, -14.76, -16.6, 0.01423, 1, 4, 11.92, -15.62, 1, 1, 4, 4.76, -18.33, 1, 1, 4, -12.86, -6.13, 1, 1, 4, -2.34, 9.06, 1, 1, 4, 9.94, 14.34, 1, 2, 4, 28.56, 15.45, 0.96856, 5, -17.21, 11.27, 0.03144, 2, 4, 45.32, 13.43, 0.243, 5, -0.52, 13.79, 0.757, 1, 5, 14.73, 33.19, 1, 1, 5, 34.54, 36.29, 1, 1, 5, 76.67, 21.58, 1, 1, 5, 58.52, -30.4, 1, 1, 4, 39.45, -1.78, 1], "edges": [26, 28, 28, 0, 0, 2, 2, 4, 4, 30, 30, 20, 20, 22, 24, 26, 22, 24, 20, 18, 18, 16, 12, 14, 16, 14, 4, 6, 6, 8, 12, 10, 8, 10]}}, "1-15yanjin2": {"1-15yanjin2": {"type": "mesh", "hull": 11, "width": 86, "height": 121, "uvs": [1, 0.46077, 0.70495, 0.5998, 0.47673, 0.84311, 0.27025, 1, 0, 1, 0, 0.8238, 0.11811, 0.73883, 0.28836, 0.54316, 0.12897, 0.26767, 0.21048, 0, 1, 0], "triangles": [8, 9, 10, 0, 8, 10, 1, 7, 8, 0, 1, 8, 2, 7, 1, 6, 7, 2, 5, 3, 4, 6, 3, 5, 6, 2, 3], "vertices": [2, 6, 91.31, -24.22, 0.18988, 7, 18.46, -37.55, 0.81012, 2, 6, 62.32, -14.91, 0.82695, 7, -2.95, -15.91, 0.17305, 1, 6, 27.07, -17.98, 1, 1, 6, 1.16, -15.97, 1, 1, 6, -13.36, 2.18, 1, 1, 6, 3.29, 15.49, 1, 2, 6, 17.66, 13.99, 0.99472, 7, -29.21, 30.36, 0.00528, 2, 6, 45.29, 17.34, 0.30144, 7, -3.15, 20.57, 0.69856, 1, 7, 26.91, 40.46, 1, 1, 7, 60.04, 39.84, 1, 2, 6, 134.85, 10.61, 1e-05, 7, 73.16, -26.78, 0.99999], "edges": [18, 20, 18, 16, 16, 14, 14, 12, 8, 10, 12, 10, 6, 8, 6, 4, 4, 2, 0, 20, 2, 0, 2, 14]}}, "1-15zuiba": {"1-15zuiba": {"type": "mesh", "hull": 4, "width": 79, "height": 31, "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 8, -17.6, -41.78, 1, 1, 8, -16.86, 37.22, 1, 1, 8, 14.14, 36.93, 1, 1, 8, 13.4, -42.06, 1], "edges": [0, 2, 2, 4, 4, 6, 0, 6]}}}}], "animations": {"animation": {"bones": {"bone2": {"scale": [{"curve": 0.25, "c3": 0.75}, {"time": 0.0667, "x": 0.97, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "curve": "stepped"}, {"time": 0.2667, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "x": 0.97, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "curve": "stepped"}, {"time": 0.5333, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "x": 0.97, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "curve": "stepped"}, {"time": 0.8, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "x": 0.97, "curve": 0.25, "c3": 0.75}, {"time": 1}]}, "bone3": {"rotate": [{"angle": -2.37, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.0667, "angle": -4.75, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": -4.75, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": -4.75, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": -4.75, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1, "angle": -2.37}]}, "bone4": {"rotate": [{"angle": -2.21, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.1, "angle": -11.99, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -11.99, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -11.99, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": -11.99, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 1, "angle": -2.21}]}, "bone5": {"rotate": [{"angle": 2.57, "curve": 0.375, "c2": 0.5, "c3": 0.75}, {"time": 0.0667, "angle": 5.14, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 5.14, "curve": 0.25, "c3": 0.75}, {"time": 0.4333, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "angle": 5.14, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "curve": 0.25, "c3": 0.75}, {"time": 0.8333, "angle": 5.14, "curve": 0.25, "c3": 0.75}, {"time": 0.9333, "curve": 0.25, "c3": 0.625, "c4": 0.5}, {"time": 1, "angle": 2.57}]}, "bone6": {"rotate": [{"angle": 2.15, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.1, "angle": 11.65, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 11.65, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 11.65, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "curve": 0.25, "c3": 0.75}, {"time": 0.8667, "angle": 11.65, "curve": 0.25, "c3": 0.75}, {"time": 0.9667, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 1, "angle": 2.15}]}, "bone7": {"translate": [{"x": 2.49, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "x": 4.82, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "x": -1.5, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": 4.82, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "x": -1.5, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "x": 4.82, "curve": 0.25, "c3": 0.75}, {"time": 0.9, "x": -1.5, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 1, "x": 2.49}]}}}}}